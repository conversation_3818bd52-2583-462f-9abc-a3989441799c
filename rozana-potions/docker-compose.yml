services:
  db1:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=potions
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=rozana^1234
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  api:
    container_name: api_service
    build:
      context: ./app
    volumes:
      - ./app/potions:/application
    ports:
      - "8004:8004"
    env_file:
      - .env
    depends_on:
      db1:
        condition: service_healthy
      redis:
        condition: service_healthy
    stdin_open: true
    tty: true
    command: python manage.py runserver 0.0.0.0:8004

  celery_worker:
    container_name: celery_worker
    build:
      context: ./app
    env_file:
      - .env
    depends_on:
      db1:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: ['celery', '-A', 'potions.celery', 'worker', '-l', 'info']

  celery_beat:
    container_name: celery_beat
    build:
      context: ./app
    env_file:
      - .env
    depends_on:
      db1:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: ['celery', '-A', 'potions.celery', 'beat', '-l', 'info', '--scheduler', 'django_celery_beat.schedulers:DatabaseScheduler']

  flower:
    container_name: flower
    build:
      context: ./app
    env_file:
      - .env
    depends_on:
      redis:
        condition: service_healthy
    ports:
      - "5555:5555"
    command: ['celery', '-A', 'potions.celery', 'flower', '--url-prefix=flower']

volumes:
  postgres_data:
from django.db import models
from core.models import BaseModel

# Create your models here.

class WMSIntegration(BaseModel):
    name = models.CharField(max_length=100)
    base_url = models.URLField(max_length=255, default='https://rzn1-be.stockone.com')
    client_id = models.CharField(max_length=255)
    client_secret = models.CharField(max_length=512)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'WMS_INTEGRATION'

    def __str__(self):
        return self.name



class LambdaIntegration(BaseModel):
    """
    Model for Lambda function integrations (Redis Lambda, Typesense Lambda, etc.)
    """
    LAMBDA_TYPES = (
        ('redis', 'Redis Lambda'),
        ('typesense', 'Typesense Lambda'),
    )

    name = models.CharField(max_length=100)
    lambda_type = models.CharField(max_length=20, choices=LAMBDA_TYPES)
    function_name = models.Char<PERSON>ield(max_length=255, help_text="AWS Lambda function name")
    aws_access_key_id = models.Char<PERSON><PERSON>(max_length=255, help_text="AWS access key ID", default="")
    aws_secret_access_key = models.CharField(max_length=512, help_text="AWS secret access key", default="")
    region = models.CharField(max_length=50, default='ap-south-1', help_text="AWS region")
    timeout = models.IntegerField(default=30, help_text="Request timeout in seconds")
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'LAMBDA_INTEGRATION'

    def __str__(self):
        return self.name


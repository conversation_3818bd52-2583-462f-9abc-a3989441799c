import uuid
import json
import base64
from typing import Dict, List, Any
from datetime import datetime, timezone

def transform_wms_inventory_to_lambda_format(wms_item: Dict[str, Any], warehouse_name: str, encode_data: bool = False) -> Dict[str, Any]:
    """
    Transform WMS inventory item data to the Lambda-compatible format.
    
    Args:
        wms_item: Single inventory item from WMS API response
        warehouse_name: Name of the warehouse (e.g., 'ROZANA_TEST_WH1')
        
    Returns:
        Transformed data in Lambda format
    """
    # Generate a unique idempotent key for this record
    idempotent_key = str(uuid.uuid4())
    
    # Transform the main inventory data
    transformed_data = {
        "sku_code": wms_item.get("sku", ""),
        "sku_desc": wms_item.get("sku_desc", ""),
        "seller_id": wms_item.get("seller_id") or None,
        "seller_name": wms_item.get("seller_name") or None,
        "cost_price": _get_cost_price_from_batches(wms_item.get("batch_details", [])),
        "batch_based": 1 if wms_item.get("batch_details") else 0,
        "is_scannable": 0,  # Default value, can be customized
        "serial_numbers": _extract_serial_numbers(wms_item.get("batch_details", [])),
        "total_quantity": int(wms_item.get("total_quantity", 0)),
        "reserved_quantity": int(wms_item.get("reserved_quantity", 0)),
        "open_order_quantity": int(wms_item.get("open_order_quantity", 0)),
        "asn_quantity": 0,  # Default value, not in WMS data
        "putaway_quantity": 0,  # Default value, not in WMS data
        "cycle_stock_quantity": 0,  # Default value, not in WMS data
        "staging_stock_quantity": 0,  # Default value, can be customized
        "available_quantity": int(wms_item.get("available_quantity", 0)),
        "batch_details": _transform_batch_details(wms_item.get("batch_details", []))
    }

    if encode_data:
        transformed_data = base64.b64encode(json.dumps({"data": transformed_data}).encode()).decode()
    
    headers = [
        {"warehouse_name": encode_data_helper(warehouse_name, ord_encode=encode_data)},
        {"idempotent_key": encode_data_helper(idempotent_key, ord_encode=encode_data)},
        {"timestamp": encode_data_helper(datetime.now(timezone.utc).isoformat(), ord_encode=encode_data)}
    ]

    # Build the complete Lambda format
    lambda_format = {
        "records": {
            "stock-updates": [
                {
                    "value": transformed_data,
                    "headers": headers
                }
            ]
        }
    }

    return lambda_format

def _get_cost_price_from_batches(batch_details: List[Dict[str, Any]]) -> float:
    """
    Extract cost price from batch details. Uses the first batch's cost price.
    
    Args:
        batch_details: List of batch detail dictionaries
        
    Returns:
        Cost price as float
    """
    if batch_details and len(batch_details) > 0:
        first_batch = batch_details[0]
        return float(first_batch.get("cost_price", 0))
    return 0.0


def _extract_serial_numbers(batch_details: List[Dict[str, Any]]) -> List[str]:
    """
    Extract all serial numbers from batch details.
    
    Args:
        batch_details: List of batch detail dictionaries
        
    Returns:
        List of serial numbers
    """
    serial_numbers = []
    for batch in batch_details:
        batch_serials = batch.get("serial_numbers", [])
        if batch_serials:
            serial_numbers.extend(batch_serials)
    return serial_numbers


def _transform_batch_details(batch_details: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Transform WMS batch details to Lambda format.
    Note: This returns an empty list as per the target format, but can be customized.
    
    Args:
        batch_details: List of batch detail dictionaries from WMS
        
    Returns:
        Transformed batch details (currently empty as per target format)
    """
    # According to your target format, batch_details is an empty array
    # If you need to include batch details, you can transform them here
    return []

def encode_data_helper(name: str, ord_encode: bool = True) -> bytes:
    if ord_encode:
        return [ord(c) for c in name]
    return name
    

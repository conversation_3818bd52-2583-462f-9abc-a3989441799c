import boto3
import json
import logging
from typing import Dict, Optional, Any
from django.conf import settings
from botocore.exceptions import ClientError, BotoCoreError

logger = logging.getLogger(__name__)


class LambdaInvocationError(Exception):
    """Raised when Lambda function invocation fails"""
    pass


class LambdaWrapper:
    """
    Generic wrapper class for AWS Lambda function invocations.

    This class handles:
    - AWS Lambda function invocations with proper authentication
    - Error handling and retry logic
    - Support for both synchronous and asynchronous invocations
    
    Note: This wrapper only pushes data to Lambda functions. Data transformation
    should be handled by the calling code before using this wrapper.
    """

    def __init__(self, region_name: str = 'ap-south-1', aws_access_key_id: Optional[str] = None, aws_secret_access_key: Optional[str] = None):
        """
        Initialize Lambda wrapper with AWS credentials.

        Args:
            region_name: AWS region name (default: 'ap-south-1')
            aws_access_key_id: AWS access key ID (if None, uses environment/IAM role)
            aws_secret_access_key: AWS secret access key (if None, uses environment/IAM role)
        """
        self.region_name = region_name

        # Initialize boto3 client with credentials
        session_kwargs = {'region_name': region_name}

        # Use provided credentials or fall back to environment/IAM role
        if aws_access_key_id and aws_secret_access_key:
            session_kwargs.update({
                'aws_access_key_id': aws_access_key_id,
                'aws_secret_access_key': aws_secret_access_key
            })
        elif hasattr(settings, 'AWS_ACCESS_KEY_ID') and hasattr(settings, 'AWS_SECRET_ACCESS_KEY'):
            session_kwargs.update({
                'aws_access_key_id': settings.AWS_ACCESS_KEY_ID,
                'aws_secret_access_key': settings.AWS_SECRET_ACCESS_KEY
            })

        try:
            self.lambda_client = boto3.client('lambda', **session_kwargs)
            logger.info(f"Lambda client initialized for region: {region_name}")
        except Exception as e:
            logger.error(f"Failed to initialize Lambda client: {e}")
            raise LambdaInvocationError(f"Failed to initialize Lambda client: {e}")

    def invoke_lambda_function(self, function_name: str, payload: Dict[str, Any], invocation_type: str = 'RequestResponse', timeout: int = 30) -> Dict[str, Any]:
        """
        Invoke AWS Lambda function with the given payload.

        Args:
            function_name: Name of the Lambda function
            payload: Data to send to the Lambda function (already prepared JSON)
            invocation_type: 'RequestResponse' (sync) or 'Event' (async)
            timeout: Function timeout in seconds

        Returns:
            Response from Lambda function

        Raises:
            LambdaInvocationError: If invocation fails
        """
        logger.info(f"Invoking Lambda function: {function_name} ({invocation_type})")

        try:
            response = self.lambda_client.invoke(
                FunctionName=function_name,
                InvocationType=invocation_type,
                Payload=json.dumps(payload, default=str)  # Handle datetime serialization
            )
            # Handle response based on invocation type
            if invocation_type == 'RequestResponse':
                # Synchronous invocation - read response
                response_payload = response['Payload'].read().decode('utf-8')

                # Check for function errors
                if response.get('FunctionError'):
                    error_msg = f"Lambda function error: {response.get('FunctionError')}"
                    logger.error(f"{error_msg}. Response: {response_payload}")
                    raise LambdaInvocationError(error_msg)

                try:
                    parsed_response = json.loads(response_payload)
                    logger.info(f"Lambda function {function_name} executed successfully")
                    return {
                        'success': True,
                        'status_code': response.get('StatusCode', 200),
                        'response': parsed_response,
                        'execution_time': response.get('ResponseMetadata', {}).get('HTTPHeaders', {}).get('x-amzn-requestid')
                    }
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse Lambda response as JSON: {e}")
                    return {
                        'success': True,
                        'status_code': response.get('StatusCode', 200),
                        'response': response_payload,
                        'raw_response': True
                    }
            else:
                # Asynchronous invocation - just return status
                logger.info(f"Lambda function {function_name} invoked asynchronously")
                return {
                    'success': True,
                    'status_code': response.get('StatusCode', 202),
                    'async_invocation': True,
                    'request_id': response.get('ResponseMetadata', {}).get('RequestId')
                }

        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"AWS ClientError invoking {function_name}: {error_code} - {error_message}")
            raise LambdaInvocationError(f"AWS error ({error_code}): {error_message}")

        except BotoCoreError as e:
            logger.error(f"BotoCoreError invoking {function_name}: {e}")
            raise LambdaInvocationError(f"AWS connection error: {e}")

        except Exception as e:
            logger.error(f"Unexpected error invoking {function_name}: {e}")
            raise LambdaInvocationError(f"Unexpected error: {e}")

    def push_data_to_lambda(self, function_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Push data to a specific Lambda function (synchronous).

        Args:
            function_name: Name of the Lambda function
            data: Prepared JSON data to send to Lambda function

        Returns:
            Result of Lambda invocation
        """
        logger.info(f"Pushing data to Lambda function: {function_name}")

        try:
            print("Pushing data to Lambda function: ", json.dumps(data))
            result = self.invoke_lambda_function(
                function_name=function_name,
                payload=data,
                invocation_type='RequestResponse'  # Use sync for better error handling
            )

            logger.info(f"Successfully pushed data to {function_name}")
            return result

        except LambdaInvocationError as e:
            logger.error(f"Failed to push data to {function_name}: {e}")
            raise

    def push_data_to_lambda_async(self, function_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Push data to a specific Lambda function (asynchronous for high throughput).

        Args:
            function_name: Name of the Lambda function
            data: Prepared JSON data to send to Lambda function

        Returns:
            Result of async Lambda invocation
        """
        # logger.info(f"Pushing data to Lambda function asynchronously: {function_name}")
        print("Pushing data to Lambda function asynchronously: ", json.dumps(data))

        try:
            result = self.invoke_lambda_function(
                function_name=function_name,
                payload=data,
                invocation_type='Event'  # Async for high throughput
            )

            logger.info(f"Successfully queued data to {function_name}")
            return result

        except LambdaInvocationError as e:
            logger.error(f"Failed to push data to {function_name}: {e}")
            raise

    def batch_push_data_to_lambda_async(self, function_name: str, data_batch: list, max_concurrent: int = 100) -> Dict[str, Any]:
        """
        Push multiple data items to Lambda function asynchronously with concurrency control.

        Args:
            function_name: Name of the Lambda function
            data_batch: List of data items to send to Lambda function
            max_concurrent: Maximum number of concurrent invocations

        Returns:
            Summary of batch invocation results
        """
        import concurrent.futures
        import time

        logger.info(f"Starting batch async push to {function_name}: {len(data_batch)} items")
        start_time = time.time()

        successful_invocations = 0
        failed_invocations = 0
        errors = []

        def invoke_single(data_item):
            try:
                return self.push_data_to_lambda_async(function_name, data_item)
            except Exception as e:
                return {'error': str(e), 'data': data_item}

        # Process in batches to control concurrency
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_concurrent) as executor:
            future_to_data = {executor.submit(invoke_single, data): data for data in data_batch}

            for future in concurrent.futures.as_completed(future_to_data):
                try:
                    result = future.result()
                    if 'error' in result:
                        failed_invocations += 1
                        errors.append(result)
                    else:
                        successful_invocations += 1
                except Exception as e:
                    failed_invocations += 1
                    errors.append({'error': str(e), 'data': future_to_data[future]})

        end_time = time.time()
        duration = end_time - start_time
        throughput = len(data_batch) / duration if duration > 0 else 0

        result_summary = {
            'total_items': len(data_batch),
            'successful_invocations': successful_invocations,
            'failed_invocations': failed_invocations,
            'duration_seconds': duration,
            'throughput_per_second': throughput,
            'errors': errors[:10] if errors else []  # Limit error details
        }

        logger.info(f"Batch async push completed: {successful_invocations}/{len(data_batch)} successful, "
                   f"{throughput:.2f} invocations/sec")

        return result_summary


from django.db import models
from core.models import BaseModel
from integrations.models import WMSIntegration, LambdaIntegration

# Create your models here.

class InventorySync(BaseModel):
    """
    Configuration for inventory synchronization from WMS to multiple target systems.

    This model defines:
    - Source: WMS system to pull inventory data from
    - Targets: Multiple target systems (Redis, Typesense Lambda, etc.) to push data to
    - Facility: Which facility this sync configuration applies to
    """
    name = models.CharField(max_length=100, help_text="Descriptive name for this sync configuration")
    source_integration = models.ForeignKey(WMSIntegration, on_delete=models.CASCADE, help_text="WMS system to pull data from")
    target_lambda_integrations = models.ManyToManyField(
        LambdaIntegration,
        blank=True, 
        help_text="Lambda integrations (Redis Lambda, Typesense Lambda, etc.) to push inventory data to"
    )
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'INVENTORY_SYNC'
        unique_together = ('name',)

    def __str__(self):
        return f"{self.name} - {self.is_active}"


class InventorySyncLog(BaseModel):

    STATUS_CHOICES = (
        ('success', 'Success'),
        ('in_progress', 'In Progress'),
        ('failed', 'Failed')
    )
    inventory_sync = models.ForeignKey(InventorySync, on_delete=models.CASCADE)
    status = models.CharField(max_length=100, choices=STATUS_CHOICES)
    error_message = models.TextField(null=True, blank=True)
    process_start_time = models.DateTimeField(auto_now_add=True)
    process_end_time = models.DateTimeField(auto_now=True)
    process_duration = models.DurationField()
    procesed_records = models.IntegerField()
    failed_records = models.IntegerField()


    class Meta:
        db_table = 'INVENTORY_SYNC_LOG'

    def __str__(self):
        return f"{self.inventory_sync} - {self.status}"

class FacilityInventorySync(BaseModel):
    
    facility = models.ForeignKey('core.Facility', on_delete=models.CASCADE)
    inventory_sync = models.ForeignKey(InventorySync, on_delete=models.CASCADE)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'FACILITY_INVENTORY_SYNC'
        unique_together = ('facility', 'inventory_sync')
    
    def __str__(self):
        return f"{self.facility} - {self.inventory_sync}"

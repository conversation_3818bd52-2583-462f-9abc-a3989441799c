from django.contrib import admin
from .models import InventorySync, InventorySyncLog, FacilityInventorySync

# Register your models here.

@admin.register(InventorySync)
class InventorySyncAdmin(admin.ModelAdmin):
    list_display = ('name', 'source_integration', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at', 'source_integration')
    search_fields = ('name', 'source_integration__name')
    readonly_fields = ('created_at', 'updated_at')
    autocomplete_fields = ('source_integration',)
    filter_horizontal = ('target_lambda_integrations',)

    fieldsets = (
        ('Basic Configuration', {
            'fields': ('name', 'is_active')
        }),
        ('Source Integration', {
            'fields': ('source_integration',)
        }),
        ('Target Integrations', {
            'fields': ('target_lambda_integrations',),
            'description': 'Select multiple target systems to push inventory data to'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
        })
    )

@admin.register(InventorySyncLog)
class InventorySyncLogAdmin(admin.ModelAdmin):
    list_display = ('inventory_sync', 'status', 'process_start_time', 'process_end_time', 'procesed_records', 'failed_records')
    list_filter = ('status', 'process_start_time', 'inventory_sync__name')
    search_fields = ('inventory_sync__name', 'error_message')
    readonly_fields = ('created_at', 'updated_at', 'process_start_time', 'process_end_time')
    raw_id_fields = ('inventory_sync',)
    date_hierarchy = 'process_start_time'

    fieldsets = (
        ('Sync Information', {
            'fields': ('inventory_sync', 'status')
        }),
        ('Process Details', {
            'fields': ('process_start_time', 'process_end_time', 'process_duration', 'procesed_records', 'failed_records')
        }),
        ('Error Information', {
            'fields': ('error_message',),
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
        })
    )

@admin.register(FacilityInventorySync)
class FacilityInventorySyncAdmin(admin.ModelAdmin):
    list_display = ('facility', 'inventory_sync', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at', 'facility', 'inventory_sync')
    search_fields = ('facility__name', 'inventory_sync__name')
    readonly_fields = ('created_at', 'updated_at')
    autocomplete_fields = ('facility', 'inventory_sync')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('facility', 'inventory_sync', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
        })
    )


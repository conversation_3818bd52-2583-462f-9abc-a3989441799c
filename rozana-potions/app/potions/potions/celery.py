import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'potions.settings')

REDIS_BROKER_URL = os.environ.get('REDIS_BROKER_URL', 'redis://redis:6379/0')

app = Celery(
    'potions',
    broker_url=REDIS_BROKER_URL,
    include=['scripts.tasks']
)

# Configure Redis as broker and result backend
app.conf.timezone = 'Asia/Kolkata'
app.conf.result_backend = REDIS_BROKER_URL
app.conf.result_expires = os.environ.get('CELERY_RESULT_EXPIRES', 3600)

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
# - namespace='CELERY' means all celery-related configuration keys
#   should have a `CELERY_` prefix.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Django Celery Beat Configuration
app.conf.beat_scheduler = 'django_celery_beat.schedulers:DatabaseScheduler'

@app.task(bind=True, ignore_result=True)
def debug_task(self):
    print(f'Request: {self.request!r}')

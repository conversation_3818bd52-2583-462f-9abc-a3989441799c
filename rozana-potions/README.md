# Potions 🧪✨

**<PERSON><PERSON><PERSON>'s Magical Automation Laboratory**

*"Every problem at Rozana has a potion. Every script is magic."*

Welcome to Potions - <PERSON><PERSON><PERSON>'s enchanted workshop where mundane operational challenges are transformed into elegant, automated solutions. Here, every script is carefully crafted like a magical potion, each one designed to solve specific problems with precision and grace.

## 🪄 The Magic Behind Potions

At Rozana, we believe that great automation feels like magic. When inventory syncs seamlessly across systems, when data flows effortlessly between platforms, when complex workflows execute flawlessly in the background - that's not just code, that's a well-brewed potion at work.

### Our Philosophy
- **Every Script is a Potion**: Each automation we create is carefully crafted with intention, tested for potency, and designed to solve real problems
- **Magic Through Simplicity**: The most powerful potions often have the simplest ingredients - clean code, clear purpose, reliable execution
- **Continuous Brewing**: Our laboratory never sleeps. We're always concocting new potions and refining existing ones
- **Shared Spellbook**: Every potion recipe is documented, shared, and improved by our community of digital alchemists

## 🎯 What Potions Delivers

Our magical laboratory provides:
- **🔄 Synchronization Potions**: Keep inventory data flowing smoothly between WMS systems and targets
- **⚡ Background Elixirs**: Handle complex tasks behind the scenes with Celery-powered magic
- **🔗 Integration Brews**: Connect disparate systems with seamless data transformation
- **⏰ Scheduled Enchantments**: Automate routine workflows with precision timing

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   WMS Systems   │───▶│     Potions     │───▶│  Target Systems │
│  (StockOne)     │    │   (Django App)  │    │ (Redis/Lambda)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │ Celery Workers  │
                       │ (Background)    │
                       └─────────────────┘
```

## 📦 Available Scripts & Integrations

### 🔄 Inventory Synchronization
**Location**: `integrations/services/`

#### WMS Integration
- **WMS Authentication Service** (`wms/auth.py`)
  - OAuth2 client credentials flow
  - Token caching and automatic refresh
  - Generic authenticated request handling

- **WMS Inventory Wrapper** (`wms/inventory.py`)
  - Paginated inventory data retrieval
  - Warehouse-specific operations
  - Error handling and logging

#### Data Transformation
- **Transformation Service** (`transformation/main.py`)
  - WMS to Lambda format conversion
  - Batch processing capabilities
  - Idempotent key generation
  - Serial number extraction

#### Target Integrations
- **Redis Integration**: Direct inventory updates to Redis stores
- **Lambda Integration**: Support for Typesense, Elasticsearch, and custom Lambda functions
- **Multi-target Sync**: Simultaneous updates to multiple destinations

### 📊 Models & Configuration

#### Integration Models
- **WMSIntegration**: WMS system configurations (StockOne, etc.)
- **OMSRedisIntegration**: Redis target configurations
- **LambdaIntegration**: Lambda function configurations (Typesense, Elasticsearch, Custom)

#### Sync Management
- **InventorySync**: Multi-target sync job configurations
  - Warehouse-specific syncing
  - Configurable batch sizes
  - Multiple target support (Redis + Lambda)
  - Frequency-based scheduling

## 🧙‍♂️ The Potions Master's Workshop

Every great alchemist needs the right tools and environment. Our workshop is powered by:

- **🏺 Django Cauldron**: Our primary brewing vessel where all potions are mixed
- **⚗️ Celery Distillery**: Background processing magic that never stops working
- **📚 PostgreSQL Grimoire**: Where all our potion recipes and results are stored
- **🔥 Redis Furnace**: The eternal flame that powers our background magic

### The Art of Potion Making

Creating a new potion at Rozana follows our sacred ritual:
1. **🔍 Identify the Problem**: What manual process needs automation magic?
2. **📝 Design the Recipe**: Plan the ingredients (data sources, transformations, outputs)
3. **🧪 Brew in the Lab**: Code with care, test thoroughly, document clearly
4. **✨ Cast the Spell**: Deploy with confidence, monitor for effectiveness
5. **📖 Update the Spellbook**: Share knowledge so others can learn and improve

## 🔧 Configuration

### Environment Variables
```bash
# Database
DATABASE_URL=************************************/potions

# Celery
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Django
SECRET_KEY=your-secret-key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1
```

### Integration Setup
1. **WMS Integration**: Configure OAuth2 credentials in Django admin
2. **Redis Integration**: Set up Redis connection details
3. **Lambda Integration**: Configure AWS Lambda functions or custom endpoints
4. **Inventory Sync**: Create sync jobs with source and target mappings

## 📋 Usage Examples

### Setting up Inventory Sync
```python
# Create WMS integration
wms_integration = WMSIntegration.objects.create(
    name="StockOne Production",
    base_url="https://api.stockone.com",
    client_id="your_client_id",
    client_secret="your_client_secret",
    warehouse="ROZANA_TEST_WH1"
)

# Create target integrations
redis_integration = OMSRedisIntegration.objects.create(
    name="Production Redis",
    host="redis.rozana.com",
    port=6379
)

lambda_integration = LambdaIntegration.objects.create(
    name="Typesense Sync",
    lambda_type="typesense",
    function_name="inventory-sync-typesense"
)

# Create sync job
inventory_sync = InventorySync.objects.create(
    name="Production Inventory Sync",
    source_integration=wms_integration,
    warehouse="ROZANA_TEST_WH1",
    sync_frequency_minutes=30,
    batch_size=100
)

# Add targets
inventory_sync.target_redis_integrations.add(redis_integration)
inventory_sync.target_lambda_integrations.add(lambda_integration)
```

## 🔍 Monitoring & Logs

- **Django Admin**: `/admin/` - Manage integrations and sync jobs
- **Celery Monitoring**: Monitor background tasks and their status
- **Application Logs**: Docker logs for debugging and monitoring

## 🛠️ Development

### Adding New Scripts
1. Create new Django app: `python manage.py startapp your_script`
2. Add models in `models.py`
3. Create Celery tasks in `tasks.py`
4. Register admin interfaces
5. Add to `INSTALLED_APPS`

### Testing
```bash
# Run tests
docker-compose exec api_service python manage.py test

# Test specific integration
python manage.py shell
>>> from integrations.services.wms.test_wms import test_wms_integration
>>> test_wms_integration()
```

## 📚 Documentation

- **Django Models**: See `integrations/models.py` for data structures
- **API Services**: Check `integrations/services/` for implementation details
- **Celery Tasks**: Background job definitions in respective `tasks.py` files

## 🤝 Contributing

1. Create feature branch from `main`
2. Implement changes with tests
3. Update documentation
4. Submit pull request

## 📄 License

Internal Rozana project - All rights reserved.

---

**Maintained by**: Rozana Engineering Team  
**Contact**: For questions or support, reach out to the development team.
